.park{
  width: 100%;
}
.park-img{
  width: 100%;
  padding-bottom: 56.25%;
  height: 0;
  position: relative;
}
.park-img-item{
  width: 100%;
  height: 100%;
  position: absolute;
}
.park-img-btngroup{
  width: 100%;
  height: 40px;
  position: absolute;
  bottom: 0;
  left: 0;
  text-align: center;
}
.park-img-btn{
  width: 64px;
  height: 29px;
  border-radius: 16.5px;
  font-size: 12px;
  display: inline-block;
  line-height: 29px;
  border: none;
}
.park-img-btn-active{
  width: 64px;
  height: 29px;
  border-radius: 16.5px;
  font-size: 12px;
  letter-spacing: 0;
  display: inline-block;
  line-height: 29px;
  border: none;
  background: #0095FF;
  color: #FFFFFF;
}
.park-img-btn-leave{
  width: 64px;
  height: 29px;
  border-radius: 16.5px;
  font-size: 12px;
  letter-spacing: 0;
  display: inline-block;
  line-height: 29px;
  border: none;
  background: rgba(255,255,255,0.78);
  color: #4A4A4A;
}
.park-img-width{
  margin: 0 2px;
}
.park-card{
  padding: 16px;
  background: #FFFFFF;  
}
.park-name{
  font-size: 16px;
  color: #333333;
  letter-spacing: 0.5px;
  line-height: 16px;
}
.park-line{
  opacity: 0.5;
  border: 1px solid #EDEDED;
}
.park-info{
  font-size: 12px;
  letter-spacing: 0.58px;
  line-height: 14px;
}
.park-info-label{
  color: #9B9B9B;
}
.park-info-text{
  color: #000000;
}
.update-carnumber{
  float: right;
  color: #07c160;
}
.park-info-fee{
  width: 50%;
  float: right;
  text-align: right;
}
.park-pay{
  width: 100%;
  text-align: right;
}
.park-pay-label{
  font-size: 12px;
  color: #000000;
  letter-spacing: 0.58px;
  line-height: 14px;
}
.park-pay-money{
  font-size: 24px;
  color: #000000;
  text-align: right;
}
.park-btn{
  background: #07c160;
  font-size: 16px;
  color: #FFFFFF;  
}
.park-btn-group{
  padding: 20px 16px;  
}
.perpay-title{
  padding: 20rpx 0;
  text-align: center;
}

