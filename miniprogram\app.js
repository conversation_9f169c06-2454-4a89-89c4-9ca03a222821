//app.js
const BASE_URL = 'https://s.bolink.club'
const CLOUD_URL = 'https://m.bolink.club'
App({
  globalData: {},
  baseUrl: BASE_URL,
  cloudUrl: CLOUD_URL,
  otherMini: true,
  type:'order',
  onLaunch: function (options) {
    console.log('onLaunch options--->',options);
    var query = options.referrerInfo.extraData || options.query;
    if((typeof query) == 'string'){
      query = JSON.parse(query)
    }
    if (query) {
      this.globalData = query;
      this.otherMini = true;
    }else{
      this.otherMini = false;
    }
    this.toggleRequestAddress(this)
    console.log("onLaunch Base Path: ", this.baseUrl);
  },
  onShow:function(options){
    wx.hideHomeButton();
    var query = options.referrerInfo.extraData || options.query;
    if((typeof query) == 'string'){
      query = JSON.parse(query)
    }
    if (query) {
      this.globalData = query;
      this.otherMini = true;
    }else{

    }
    this.toggleRequestAddress(this)
  },
  /**
   * 根据第三方传入的参数，改变请求地址
   * @param {*} app 
   */
  toggleRequestAddress(app) {
    const query = app.globalData
    if (query && query.pay_url) {
      app.baseUrl = query.pay_url
    } else {
      app.baseUrl = BASE_URL
    }
  }
})