<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="ca277b09-e0f6-4ec1-b97b-2084c74004a5" name="Default Changelist" comment="">
      <change beforePath="$PROJECT_DIR$/app.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/app.wxss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/components/coupon-item/index.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/components/coupon-item/index.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/components/coupon-item/index.wxml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/components/coupon-item/index.wxss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/components/keyboard/assets/images/arrow_down.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/components/keyboard/assets/images/del-icon.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/components/keyboard/index.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/components/keyboard/index.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/components/keyboard/index.wxml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/components/keyboard/index.wxss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/components/pagination/index.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/components/pagination/index.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/components/pagination/index.wxml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/components/pagination/index.wxss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/image/coupon/coupon_abandoned.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/image/coupon/coupon_expired.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/image/coupon/coupon_hour.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/image/coupon/coupon_money.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/image/coupon/coupon_used.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/image/keyboard-active.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/image/keyboard.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/pages/coupon/index.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/pages/coupon/index.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/pages/coupon/index.wxml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/pages/coupon/index.wxss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/pages/index/index.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/pages/index/index.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/pages/index/index.wxml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/pages/index/index.wxss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/pages/order/order.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/pages/order/order.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/pages/order/order.wxml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/pages/order/order.wxss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/pages/park/park.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/pages/park/park.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/pages/park/park.wxml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/pages/park/park.wxss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/pages/pay/pay.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/pages/pay/pay.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/pages/pay/pay.wxml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/pages/pay/pay.wxss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/pages/result/result.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/pages/result/result.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/pages/result/result.wxml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/pages/result/result.wxss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/pages/test/test.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/pages/test/test.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/pages/test/test.wxml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/pages/test/test.wxss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/project.config.json" beforeDir="false" afterPath="$PROJECT_DIR$/project.config.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/project.private.config.json" beforeDir="false" afterPath="$PROJECT_DIR$/project.private.config.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sitemap.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/utils/util.js" beforeDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="master_bank_1013" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/doc/README.md" root0="FORCE_HIGHLIGHTING" root1="SKIP_INSPECTION" />
  </component>
  <component name="ProjectId" id="1zLoozU8u8imyUBeV3FEucwLjuI" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">
    <property name="RunOnceActivity.OpenProjectViewOnStart" value="true" />
    <property name="RunOnceActivity.ShowReadmeOnStart" value="true" />
    <property name="WebServerToolWindowFactoryState" value="false" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$" />
    <property name="vue.rearranger.settings.migration" value="true" />
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="ca277b09-e0f6-4ec1-b97b-2084c74004a5" name="Default Changelist" comment="" />
      <created>1633936907916</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1633936907916</updated>
      <workItem from="1633936911464" duration="119000" />
      <workItem from="1634092229193" duration="4154000" />
      <workItem from="1634261635535" duration="81000" />
      <workItem from="1634278642238" duration="2041000" />
      <workItem from="1634523680897" duration="648000" />
      <workItem from="1646362398456" duration="313000" />
      <workItem from="1664435415370" duration="354000" />
      <workItem from="1666347078661" duration="946000" />
      <workItem from="1666663399133" duration="1545000" />
    </task>
    <task id="LOCAL-00001" summary="领取优惠券-暂存">
      <created>1633936978925</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1633936978925</updated>
    </task>
    <task id="LOCAL-00002" summary="银行支付，包含优惠券">
      <created>1634179325548</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1634179325548</updated>
    </task>
    <task id="LOCAL-00003" summary="首页切换为index">
      <created>1634180072129</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1634180072129</updated>
    </task>
    <task id="LOCAL-00004" summary=".">
      <created>1634280703433</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1634280703433</updated>
    </task>
    <task id="LOCAL-00005" summary="尚峰广场1.0.0">
      <created>1634623135660</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1634623135660</updated>
    </task>
    <task id="LOCAL-00006" summary="[feat][无]:支持自定义传入请求地址">
      <created>1664435560720</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1664435560720</updated>
    </task>
    <option name="localTasksCounter" value="7" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
    <option name="oldMeFiltersMigrated" value="true" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="领取优惠券-暂存" />
    <MESSAGE value="银行支付，包含优惠券" />
    <MESSAGE value="首页切换为index" />
    <MESSAGE value="." />
    <MESSAGE value="尚峰广场1.0.0" />
    <MESSAGE value="[feat][无]:支持自定义传入请求地址" />
    <option name="LAST_COMMIT_MESSAGE" value="[feat][无]:支持自定义传入请求地址" />
  </component>
</project>