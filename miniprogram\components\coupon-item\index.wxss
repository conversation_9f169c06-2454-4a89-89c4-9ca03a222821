/* components/coupon-item/index.wxss */
.coupon-item {
  margin: 0 32rpx 32rpx 32rpx;
  height: 206rpx;
  display: flex;
  background-color: #ffffff;
  box-shadow: 0 4rpx 8rpx 0 rgba(0, 0, 0, 0.04);
}
.coupon-item__icon {
  position: relative;
  flex: 0 0 96rpx;
  margin-right: 32rpx;
}
.coupon-item__icon image {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
}
.coupon-item__icon__text {
  position: absolute;
  left: 24rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 32rpx;
  font-size: 24rpx;
  color: #ffffff;
  line-height: 33rpx;
}
.coupon-item__content {
  /* width: 300rpx; */
  display: flex;
  flex-direction: column;
  justify-items: center;
  justify-content: center;
}
.coupon-item__nt {
  height: 80rpx;
  margin-bottom: 24rpx;
}
.coupon-item__name {
  line-height: 1;
  margin-bottom: 12rpx;
  color: rgba(89, 89, 89, 1);
  font-size: 28rpx;
  font-weight: 400;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.coupon-item__type {
  line-height: 1;
  color: rgba(0,0,0, 1);
  font-size: 28rpx;
  font-weight: bolder;
}
.coupon-item__date {
  line-height: 1;
  color: rgba(153, 153, 153, 1);
  font-size: 24rpx;
  font-weight: 400;
}
.coupon-item__box {
  margin-right: 32rpx;
  flex: 1;
  text-align: right;
  display: flex;
  flex-direction: column;
  justify-items: center;
  justify-content: center;
}
/* 小时券 */
.coupon-item_color_1 {
  color: rgba(47, 195, 71, 1);
}
/* 现金券 */
.coupon-item_color_2 {
  color: rgba(255, 139, 19, 1);
}
/* 已使用 */
.coupon-item_color_3 {
  color: rgba(221, 226, 236, 1);
}
/* 已过期 */
.coupon-item_color_4 {
  color: rgba(236, 221, 221, 1);
}
/* 已废弃 */
.coupon-item_color_5 {
  color: rgba(221, 236, 230, 1);
}

.coupon-item__money__count {
  line-height: 1;
  font-size: 80rpx;
  font-weight: bold;
}
.coupon-item__unit {
  line-height: 1;
  font-size: 32rpx;
  font-weight: 600;
}
.coupon-item__money__unit {
  vertical-align: top;
  margin-right: 10rpx;
}

.coupon-item__tip {
  font-size: 24rpx;
}