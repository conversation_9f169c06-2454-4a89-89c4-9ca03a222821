# 互联互通停车费支付

## 通用支付
- - -
### 添加插件
使用插件前，在项目根目录app.json中声明本插件(根据微信开发者工具控制台提示添加插件） 注意：插件版本号更新后开发工具会有提示，可根据提示修改为最新的插件版本号

代码示例：
```json
"plugins": {
    "pay-plugin": {
      "version": "1.0.0",
      "provider": "wxbd08b4baa10fcc1d"
    }
  }
```
### 使用插件
需携带参数 pre_order_info='{}' 通用支付的支付信息
在需要跳转到插件页面的 wxml 中，url 使用 plugin:// 前缀，形如 plugin://PLUGIN_NAME/PLUGIN_PAGE， 如：

代码示例：
```js
<navigator url="plugin://pay-plugin/payment?pre_order_info=orderInfo">
      去缴费
</navigator>
```
*注：pre_order_info的参数需要序列化，参数避免使用'='等特殊字符*
```js
orderInfo = JSON.stringify({'union_id: '12345','xxx':'xxx'})
```
#### 支付后的回调
*注：支付成功/失败 两秒后自动返回*
```js
const plugin = requirePlugin('pay-plugin')
onShow() {
    const callback = plugin.getPaymentResult()
    console.log(callback) // { errMsg: "requestPluginPayment:fail requestPayment:fail cancel" }
}
```
