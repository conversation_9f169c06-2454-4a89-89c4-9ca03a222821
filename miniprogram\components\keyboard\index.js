// components/keyboard/index.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    defaultVal:{
      type:String,
      value:'',
      observer: function (newData, oldData) {
        setTimeout(() => {
          this.setData({
            carNumberVal: newData
          })
        }, 60)
      }
    },
    maxLen:{
      type:Number,
      value:8,
    },
    maskState: {
      type:Boolean,
      value:true,
      observer: function (newData, oldData) {
        setTimeout(() => {
          let len = this.data.carNumberVal.length
          this.setData({
            showMask: newData,
            hideKeyboard: len > 0 ? false : true,
            valueLen: len
          })
      
        }, 60)

      }
    },
    clearCarNumber:{
      type:Boolean,
      value:false,
      observer: function(newData,oldData){
        setTimeout(()=>{
          if(newData){
            this.setData({
              hideKeyboard: true,
              carNumberVal: '',
              valueLen: 0
            })
            this.triggerEvent('currentVal', '')
          }
        },60)
      }
    },
    count:{
      type: Number,
      value: 0,
      observer: function (newData, oldData) {
        setTimeout(() => {
            this.setData({
              hideKeyboard: true,
              carNumberVal: '',
              valueLen: 0
            })
            this.triggerEvent('currentVal', '')
        }, 60)
      }
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    keyboard:{
      provinceItemFirst: ['京', '沪', '浙', '苏', '粤', '鲁', '晋', '冀', '渝','豫'],
      provinceItemSecond: ['川', '辽', '吉', '黑', '湘', '鄂', '皖', '赣', '闽'],
      provinceItemThird: ['陕', '甘', '宁', '蒙', '津', '贵', '云', '桂'],
      provinceItemFourth: ['琼', '青', '新', '藏', '使'],
      numericKey:['1','2','3','4','5','6','7','8','9','0'],
      letterKeyFirst: ['Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'P', '港', '澳'],
      letterKeySecond: ['A', 'S', 'D', 'F', 'G', 'H', 'J', 'K', '学', '领'],
      letterKeyThird:['Z','X','C','V','B','N','M','L'],
    },
    showMask:true,
    hideKeyboard:true,
    carNumberVal:'',
    valueLen:0,
  },

  /**
   * 组件的方法列表
   */
  pageLifetimes:{
    show(){
      this.setData({
        hideKeyboard: true,
        valueLen: 0
      })
      this.triggerEvent('currentVal', this.data.carNumberVal)
    },
  },
  methods: {
    clickkey(e){
      let val = e.currentTarget.dataset.objectText
      let stopLen = this.data.carNumberVal.length -1;
      if(val == 'del'){
        this.data.carNumberVal = this.data.carNumberVal.substring(0, stopLen);
      } else if (this.data.carNumberVal.length>=this.data.maxLen){

      }
      else{
        this.data.carNumberVal += val;
      }
      if (this.data.carNumberVal.length>=1){
        this.data.hideKeyboard = false;
      }else{
        this.data.hideKeyboard = true;
      }
      this.setData({
        hideKeyboard: this.data.hideKeyboard,
        valueLen: this.data.carNumberVal.length
      })
      this.triggerEvent('currentVal', this.data.carNumberVal)
    },
    close(e){
      this.setData({
        showMask:false
      })
      this.triggerEvent('close', false)
    }
  }
})
