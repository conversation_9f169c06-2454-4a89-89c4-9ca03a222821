/* pages/coupon/index.wxss */
page {
  height: 100%;
  background: #EDEDED;
}
.coupon-container{
  position:relative;
  height: 100%;
}
.size-box {
  height: 120rpx;
}
.nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  justify-items: center;
  text-align: center;
  height: 88rpx;
  background-color: #fff;
}
.nav-item {
  position: relative;
  flex: 1;
  font-size: 28rpx;
  color: rgba(89, 89, 89, 1);
}
.nav-item_actived {
  color: rgba(4, 190, 34, 1);
}
.nav-item_actived:after {
  content: '';
  position: absolute;
  bottom: -26rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 140rpx;
  height: 2rpx;
  background-color: rgba(4, 190, 34, 1);
}