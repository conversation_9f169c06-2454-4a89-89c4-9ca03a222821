// functional-pages/request-payment.js
exports.beforeRequestPayment = function (paymentArgs, callback) {
  const { BASE_URL, PAYMENT_URL } = require('./URL');
  const customArgument = paymentArgs;
  console.log('get query url: ', `${BASE_URL}${PAYMENT_URL}`)
  console.log('custom argument--->', customArgument)
  wx.login({
    success: function (data) {
      customArgument.code = data.code;
      wx.request({
        url: `${BASE_URL}${PAYMENT_URL}`,
        method: 'POST',
        data: customArgument,
        header: {
          'content-type': 'application/json' // 默认值
        },
        success(res) {
          const data = res.data;
          if(data.state === 1){
            const payargs = data.data;
            const error = null;
            const requestPaymentArgs = {
              timeStamp: payargs.timeStamp,
              nonceStr: payargs.nonceStr,
              package: payargs.package,
              signType: payargs.signType,
              paySign: payargs.paySign,
              extraData: { // 用 extraData 传递自定义数据
              },
            };
            callback(error, requestPaymentArgs);
          } 
          else {
            callback({
              state: 0,
              errmsg: '获取下单信息失败'
            })
          }
        },
        fail: function(res) {
          console.log('拉取用户 openid 失败，将无法正常使用开放接口等服务', res);
          // callback 第一个参数为错误信息，返回错误信息
          callback(res);
        }
      })
    },
    fail: function (err) {
      console.log('wx.login 接口调用失败，将无法正常使用开放接口等服务', err)
      // callback 第一个参数为错误信息，返回错误信息
      callback(err);
    }
  })
}