// pages/coupon/index.js
const app = getApp();
import { random } from '../../utils/util'
Page({

  /**
   * 页面的初始数据
   */
  data: {
    key: null,
    unusedData:[],
    usedData:[],
    expiredData:[],
    abandonedData:[],
    couponList: [],
    couponTotal: 0,
    currentIndex: '1',
    orderId: 0,
  },
  handleItemClick(evt) {
    console.log('evt: ',evt)
    const self = this;
    const { item } = evt.detail.dataset;
    // if (item.state !== 1) {
    //   return false;
    // }
    wx.showModal({
      title: '提示',
      content: '确认使用该优惠券嘛？',
      success (res) {
        if (res.confirm) {
          wx.showLoading({
            title: '优惠券校验中...',
          })
          self.confirmCoupon(item.coupon_id);
          
        } else if (res.cancel) {
          console.log('用户点击取消')
        }
      }
    })
  },
  /**
   * 
   * @param {int} coupon_id 优惠券id
   */
  confirmCoupon(coupon_id) {
    const { union_id, park_id, plate_number} = app.globalData;
    const { orderId } = this.data;
    wx.request({
      url: app.baseUrl + '/unionapi/miniprogram/confirmCoupon',
      method: 'POST',
      data: {
        union_id: union_id,
        park_id: park_id,
        plate_number: plate_number,
        order_id: orderId,
        coupon_id: coupon_id
      },
      header: {
        'content-type': 'application/json' // 默认值
      },
      success: (res)=> {
        wx.hideLoading()
        let data = res.data;
        if (data.state === 1) {
          wx.redirectTo({
            url: '/pages/order/order?plate_number='+plate_number
              + '&union_id=' + union_id
              + '&park_id=' + park_id
          });
        } else {
          wx.showToast({
            title: data.errmsg,
            icon: 'none',
            duration: 2000
          })
        }
        
      },
      fail:()=> {
        wx.hideLoading()
        wx.showToast({
          title: '获取失败',
          icon: 'none',
          duration: 2000
        })
      }
    })
  },
  handleTabToggle(evt) {
    let { dataset } = evt.target;
    let {unusedData,
      usedData,
      expiredData,
      abandonedData} = this.data;
    if (dataset.index === '1') {
      this.setData({
        couponList: unusedData,
        couponTotal: unusedData.length
      })
    }
    else if(dataset.index === '2'){
      this.setData({
        couponList: usedData,
        couponTotal: usedData.length
      })
    }
    else if(dataset.index === '3'){
      this.setData({
        couponList: expiredData,
        couponTotal: expiredData.length
      })
    }
    else if(dataset.index === '6'){
      this.setData({
        couponList: abandonedData,
        couponTotal: abandonedData.length
      })
    }else {
      this.setData({
        couponList: [],
        couponTotal: 0
      })
    }
    
    
    this.setData({
      currentIndex: dataset.index
    })
  },
  getCouponList(e) {
    this.setData({
      couponList: [],
      couponTotal: []
    })
    const { union_id, park_id, plate_number} = app.globalData;
    wx.request({
      url: app.baseUrl + '/unionapi/miniprogram/getCouponList',
      method: 'POST',
      data: {
        union_id: union_id,
        park_id: park_id,
        plate_number: plate_number
      },
      header: {
        'content-type': 'application/json' // 默认值
      },
      success: (res)=> {
        let data = res.data;
        if (data.state === 1) {
          let baseData = data.data;
          let unusedData =  baseData.filter(item => item.state === 1);
          let usedData =  baseData.filter(item => item.state === 2);
          let expiredData =  baseData.filter(item => item.state === 3);
          let abandonedData =  baseData.filter(item => item.state === 6);
          this.setData({
            unusedData: unusedData,
            usedData: usedData,
            expiredData: expiredData,
            abandonedData: abandonedData,
            couponList: unusedData,
            couponTotal: unusedData.length
          })
        } else {
          this.setData({
            couponList: [],
            couponTotal: 0
          })
          wx.showToast({
            title: '获取失败',
            icon: 'none',
            duration: 2000
          })
          
        }
      },
      fail:()=> {
        wx.showToast({
          title: '获取失败',
          icon: 'none',
          duration: 2000
        })
      }
    })
    // const data = e.detail;
    // couponModel.getUserList(data)
    // .then(result => {
      // this.setData({
      //   couponList: result.list,
      //   couponTotal: result.total
      // })
    // })
  },
  /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
  onPullDownRefresh: function () {
      // 标题栏显示刷新图标，转圈圈
      wx.showNavigationBarLoading()
      // 请求最新数据
      setTimeout(() => {
          // 标题栏隐藏刷新转圈圈图标
          wx.hideNavigationBarLoading()
      }, 1000);
  },
  // 触底了
  onReachBottom() {
    this.setData({
      key: random(16)
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    if (options.order_id) {
      this.setData({
        orderId: options.order_id
      })
      // this.getCouponList()
    } else {
      console.log("无订单id")
    }
    
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})