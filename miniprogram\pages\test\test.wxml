<!--pages/test/test.wxml-->
<view class="page-body">
  <view class="box-size"></view>
  <view class="page-section">
    <view class="weui-cell__title">请选择支付模式</view>
    <view class="weui-cell__input">
      <radio-group bindchange="radioChange">
        <radio value="order" />查询订单
        <radio value="park" />支付页
        <radio value="pay" />通用支付
        <radio value="plugin-pay" />插件通用支付
      </radio-group>
    </view>
  </view>
  <view class="page-section">
    <view class="weui-cell__title">请填写extraData内的参数 - json</view>
    <view class="weui-cell__input">
      <textarea value="{{extraData}}" maxlength="-1" placeholder="{{placeholder}}" bindinput="handleChange" style="height: 300rpx;"></textarea>
    </view>
    <view class="tips">当选择通用支付时，extraData无需填写</view>
  </view>
  
  <view class="page-section">
    <view class="tips">当选择通用支付时，需填写以下内容</view>
    <view class="weui-cell__title">请选择平台</view>
    <view class="weui-cell__input">
      <radio-group bindchange="platformChange">
        <radio value="beta" />互联互通
        <radio value="other" />独立云
      </radio-group>
    </view>
    <view class="weui-cell__title">请填写独立云域名</view>
    <view class="weui-cell__input">
      <input type="text" value="{{platformText}}" bindinput="platformValue" placeholder="https://beta.bolink.club"/>
    </view>
  </view>
  <view class="box-size"></view>
  <view class="page-button">
  <view wx:if="{{ value === 'plugin-pay' }}">
    <button bindtap="getPayInfoFn">请先点击获取支付信息</button>
    <navigator  id="nav" url="{{'plugin://pay-plugin/payment?pre_order_info='+ payInfo}}">
      确定
    </navigator>
  </view>
    
    <button wx:else type="primary" bindtap="handleClick">确定</button>
    <button type="default" bindtap="handleReset">重置</button>
  </view>
  <view class="tips">此测试页面模拟第三方小程序进入，支付成功后，无法正常返回，如需正常返回，请联系开发人员配合</view>
</view>
