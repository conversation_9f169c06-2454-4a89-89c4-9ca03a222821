{"description": "项目配置文件，详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html", "packOptions": {"ignore": [], "include": []}, "setting": {"urlCheck": false, "es6": true, "enhance": false, "postcss": true, "preloadBackgroundData": false, "minified": true, "newFeature": true, "coverView": true, "nodeModules": true, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": false, "lazyloadPlaceholderEnable": false, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "enableEngineNative": false, "useIsolateContext": true, "userConfirmedBundleSwitch": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "disableUseStrict": false, "showES6CompileOption": false, "useCompilerPlugins": false, "minifyWXML": true, "useStaticServer": true}, "miniprogramRoot": "miniprogram/", "pluginRoot": "plugin/", "compileType": "miniprogram", "libVersion": "2.27.0", "appid": "wx0870f79235e513f7", "pluginAppid": "wxbd08b4baa10fcc1d", "projectname": "%E5%81%9C%E8%BD%A6%E8%B4%B9%E6%94%AF%E4%BB%98", "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "condition": {"plugin": {"list": []}, "game": {"list": []}, "gamePlugin": {"list": []}, "miniprogram": {"list": [{"id": 0, "name": "车牌页进入", "pathName": "pages/index/index", "query": "union_id=200279&park_id=22370", "scene": 1001}, {"id": 1, "name": "无车牌号跳转", "pathName": "pages/park/park", "query": "", "scene": 1037, "referrerInfo": {"appId": "121212121212", "extraData": "{\"union_id\":200265,\"park_id\":28888}"}}, {"id": 2, "name": "本地进入订单", "pathName": "pages/order/order", "query": "union_id=32525&park_id=28888", "scene": 1001}, {"id": 3, "name": "本地进入支付页", "pathName": "pages/park/park", "query": "union_id=200265&park_id=28888&plate_number=苏DTTTTT&money=0.01&park_name=Cyz测试停车场&order_id= A1_2C1576649522", "scene": null}, {"id": 4, "name": "小程序进入支付页", "pathName": "pages/park/park", "query": "", "scene": 1037, "referrerInfo": {"appId": "wx964385607e75e538", "extraData": "{\"union_id\":200159,\"park_id\":24063,\"plate_number\":\"苏GGGBB07\",\"in_time\":1594175890,\"money\":\"0.1\",\"order_id\":\"A1_2C1597111552\"}"}}, {"name": "小程序进入订单页", "pathName": "pages/order/order", "query": "", "scene": 1037, "referrerInfo": {"appId": "121212121212", "extraData": "{\"union_id\": 200159,\"park_id\":20180427}"}}, {"name": "小程序直接支付", "pathName": "pages/pay/pay", "query": "", "scene": 1037, "referrerInfo": {"appId": "wx964385607e75e538", "extraData": "{\"sign\":\"F8E88D548507C7F4B09681D056B94232\",\"union_id\":200712,\"data\":{\"amount\":\"0.01\",\"trade_no\":\"196E71D324EA5AD535A\",\"title\":\"测试微信跳转支持通用支付\",\"park_id\":\"36342\",\"pay_type\":0,\"description\":\"orderid=02\",\"car_number\":\"苏DDDGG02\",\"channel\":\"applets\",\"time_temp\":1609831766}}"}}]}}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "srcMiniprogramRoot": "miniprogram/"}