/* pages/result/result.wxss */
.page{
  width: 100%;
  height: 100%;
  background-color: #f2f2f2;
  font-family: PingFangSC-Regular;
  font-size: 32rpx;
}
.sections{
  position: fixed;
  left: 0;
  bottom: 20%;
  width: 100%;
}
.btn-group{
  margin: 30rpx;
}
/* button{
  height: 96rpx;
  line-height: 96rpx;
} */
.repay{
  background: #07c160;
  font-size: 32rpx;
  color: #FFFFFF;
  margin-bottom: 32rpx;
}

.pay-state-icon{
  position: relative;
  top:10%;
  text-align: center;
}
.pay-state-tip{
  padding-top: 10rpx;
  color: #07c160;
  font-size: 28rpx;
}
