// pages/park/park.js
var app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    park: {
      park_name: '',
      union_id: '',
      park_id: '',
      order_id: '',
      plate_number: '',
      money: '0.00',

    },
    perpayState:false,
    canRepay: '',
    payLoading: false,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var _self = this;
    var plateNumber = 'park.plate_number', 
        unionId = 'park.union_id',
        parkId = 'park.park_id';
        app.type = 'order';
    var plate_number = wx.getStorageSync('plate_number');
    if (app.otherMini) {//从小程序进入
      if (app.globalData.plate_number == undefined && plate_number == '') {//未携带车牌
        wx.reLaunch({
          url: '../index/index?union_id=' + app.globalData.union_id + '&park_id=' + app.globalData.park_id
        })
        return false;
      } else {

        let car_number = '';
        if (app.globalData.plate_number) {
          wx.setStorageSync('plate_number', app.globalData.plate_number)
        } else {
          app.globalData.plate_number = plate_number;
        }

        this.setData({
          [plateNumber]: app.globalData.plate_number,
          [unionId]: app.globalData.union_id,
          [parkId]: app.globalData.park_id,
        })
      }

    } else {
      if (options.plate_number || plate_number){
        if (options.plate_number) {
          app.globalData.plate_number = options.plate_number;
        }else{
          app.globalData.plate_number = plate_number;
        }
        app.globalData.union_id = options.union_id;
        app.globalData.park_id = options.park_id;
      }else{
        app.globalData.union_id = options.union_id;
        app.globalData.park_id = options.park_id;
        wx.reLaunch({
          url: '../index/index?union_id=' + options.union_id + '&park_id=' + options.park_id
        })
        return false;
      }
      this.setData({
        [plateNumber]: app.globalData.plate_number,
        [unionId]: options.union_id,
        [parkId]: options.park_id,
      })
    }

    wx.request({
      url: app.baseUrl + '/unionapi/miniprogram/getOrderInfo',
      method: 'POST',
      data: {
        plate_number: app.globalData.plate_number,
        union_id: app.globalData.union_id,
        park_id: app.globalData.park_id
      },
      header: {
        'content-type': 'application/json' // 默认值
      },
      success(res) {
        let data = res.data;
        if (data.state === 1) {
          _self.setData({
            'park': data.data
          })
          // console.log('success--->', _self.data.park)
        }else if(data.state === 2){
          _self.setData({
            'park': data.data,
            ['park.money']:'0.00',
            'canRepay':'disabled',
            'perpayState':true
          })
        } 
        else {
          _self.setData({
            'canRepay': 'disabled'
          })
          wx.showModal({
            title: '提示',
            content: data.errmsg,
            showCancel: false,
            success (res) {
              if (res.confirm) {
              } else if (res.cancel) {
              }
            }
          })
          
        }
      },
      fail(err) {
        console.log('err--->', err)
      }
    })

  },
  onShow(){
    if(app.globalData.union_id == undefined || app.globalData.union_id == ''){
      wx.showModal({
        title: '提示',
        content: '请在车场小程序重新发起支付',
        showCancel:false,
        success (res) {
          wx.navigateBackMiniProgram({
            extraData: {},
            success: (res) => {
              console.log(JSON.stringify(res));
            },
            fail: (res) => {
              console.log(JSON.stringify(res));
            }
          });
        }
      })
    }
  },
  // 获取优惠券
  handleCouponClick() {
    wx.navigateTo({
      url: '../coupon/index?order_id='+ this.data.park.order_id
    })
  },
  //修改车牌号
  editCarNumber(){
    wx.navigateTo({
      url: '../index/index?union_id=' + app.globalData.union_id + '&park_id=' + app.globalData.park_id
    })
  },
  //支付下单
  naviteToResult(event) {
    let _self = this;
    if(_self.data.payLoading){
      return false;
    }
    this.setData({
      payLoading: true
    });

    wx.login({
      success: (res) => {
        var authCode = res.code;
        this.pay(authCode)
      },
      fail: () => {
        this.setData({
          payLoading: false
        });
        wx.showToast({
          title: '获取用户授权失败',
          icon: 'none'
        });
      }
    })
  },
  pay(authCode) {
    const _self = this;
    const parkInfo = this.data.park;
    parkInfo.code = authCode;
    wx.request({
      url: `${app.baseUrl}/unionapi/miniprogram/pay`,
      method: 'POST',
      data: parkInfo,
      header: {
        'content-type': 'application/json' // 默认值
      },
      success(res) {
        let data = res.data;
        if (data.state === 1) {
          _self.goPay(data.data)
        } else {
          wx.showToast({
            title: data.errmsg,
            icon: 'none'
          });
        }
        _self.setData({
          payLoading: false
        });
      }
    })

  },
  goPay(data) {
    wx.requestPayment({
      timeStamp: data.timeStamp,
      nonceStr: data.nonceStr,
      package: data.package,
      signType: data.signType,
      paySign: data.paySign,
      success(res) {        
        if (res.errMsg == "requestPayment:ok") {
          if(app.otherMini){// 支付成功后，如果是小程序跳转过来的直接返回用户小程序
            wx.navigateBackMiniProgram({
              extraData: {
                "source":"s.bolink.club",
                "flag":app.globalData.flag,
                "errMsg":"requestPayment:ok"
              },
              success: (res) => {
                console.log(JSON.stringify(res));
              },
              fail: (res) => {
                console.log(JSON.stringify(res));
              }
            });
          }else {
            wx.redirectTo({
              url: '/pages/result/result?msg=success'
            });
          }
          
        } else {
          wx.redirectTo({
            url: '/pages/result/result?msg=fail'
          });
        }
      },
      fail(res) {
        // console.log('err res--->', res)
        if (res.errMsg == "requestPayment:fail cancel") {
          wx.showToast({
            title: '用户取消支付',
            icon: 'none'
          });
        }
      }
    })


  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})