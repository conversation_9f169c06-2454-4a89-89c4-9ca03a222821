.page{
  width: 100%;
  background-color: #f2f2f2
}
.section{
  position: relative;
  top:15px;
  margin: 0 15px;
  padding: 15px;
  background-color: white
}
button{
  margin-top: 15px;
}
.carno-label{
  margin-bottom: 15px;
}
.carno{
  width: 100%;
  padding: 0;
  height: 36px;
  line-height: 36px;
  border: #f2f2f2 solid 1px;
}
.car-number-wrapper{
  position: relative;
  height: 110rpx;
  border-bottom: 1px dotted #979797;
  margin-bottom: 32rpx;
}
.input {
  padding: 0 30rpx;
  height: 80rpx;
  background: white;
  border-radius: 10rpx;
  border: 1px solid #D9D9D9;
}
.car-number-wrapper .input{
  display: inline-block;
  width: -webkit-calc(100% - 180rpx);
  width: calc(100% - 180rpx);
}
.toggleKeyboard{
  position: absolute;
  top: 10%;
  right: 20rpx;
  padding: 0;
  margin: 0;
  width: 60rpx;
  height: 60rpx;
}
.inputremark {
  height: 80rpx;
  padding: 0 30rpx;
  border-bottom: 1px solid gainsboro;
}