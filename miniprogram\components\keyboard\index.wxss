/* components/keyboard/index.wxss */

.keyboard_mask {
  position: fixed;
  top: 400rpx;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 99;
}

.keyboard_wrapper {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 444rpx;
  z-index: 100;
  background: #fff;
  padding: 16rpx;
  box-shadow: 0px 2rpx 4rpx 4rpx rgba(0, 0, 0, 0.02),
                /*上边阴影  红色*/ 0 0 0 0,
                /*左边阴影 */ 0 0 0 0,
                /*右边阴影 */ 0 0 0 0; /*下边阴影 */
}
/* *
*关闭键盘
 */
.close_keyboard{
  position: relative;
  height: 58rpx;
  background: #fff;
}
.closeimg{
  float: right;
  width: 58rpx;
  height: 58rpx;
}
/** 
*键盘样式
*
 */

.keyboard-layout-all {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  flex-direction: column;
  height: 389rpx;
  width: 100%;
}

.keyboard-layout {
  width: 100%;
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  align-content: center;
  justify-content: center;
  font-size: 40rpx;
}

button.key-btn {
  width: 68rpx;
  height: 76rpx;
  line-height: 68rpx;
  padding: 0;
  margin: 0 5rpx;
  background: white;
  font-size: 40rpx;
  font-weight: 600;
  color: #515151;
  text-align: center;
  box-shadow: inset 0 -8rpx 0 7rpx rgba(0, 0, 0, 0.08);
  border-radius: 6rpx;
}

button.key-btn:active {
  background: darkgray;
}

button.key-btn::after {
  border: none;
}

.del-icon{
  width: 39rpx;
  height: 31rpx;
}


.van-hairline, .van-hairline--bottom, .van-hairline--left, .van-hairline--right,
.van-hairline--surround, .van-hairline--top, .van-hairline--top-bottom {
  position: relative;
}

.van-hairline--bottom::after, .van-hairline--left::after,
.van-hairline--right::after, .van-hairline--surround::after,
.van-hairline--top-bottom::after, .van-hairline--top::after,
.van-hairline::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 200%;
  height: 200%;
  -webkit-transform: scale(0.5);
  transform: scale(0.5);
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  pointer-events: none;
  box-sizing: border-box;
  border: 0 solid #e5e5e5;
}

.van-hairline--top::after {
  border-top-width: 1px;
}

.van-hairline--left::after {
  border-left-width: 1px;
}

.van-hairline--right::after {
  border-right-width: 1px;
}

.van-hairline--bottom::after {
  border-bottom-width: 1px;
}

.van-hairline--top-bottom::after {
  border-width: 1px 0;
}

.van-hairline--surround::after {
  border-width: 1px;
}
