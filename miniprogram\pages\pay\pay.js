// pages/park/park.js
var app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    park:{
      park_name:'',
      union_id:'',
      park_id:'',
      order_id:'',
      plate_number:'',
      money:'0.00',
      
    },
    canRepay: '',
    payLoading:false,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    app.type ='pay';
    if (app.otherMini){
      this.setData({
        'park': app.globalData
      })
    }else{
      app.globalData = options;
      this.setData({
        'park':app.globalData
      })
    }
    wx.showLoading({
      title: '加载中',
    })
    this.naviteToResult();
  },
  //支付下单
  naviteToResult(event) {
    let _self = this;
    if(_self.data.payLoading){
      return false;
    }
    this.setData({
      payLoading: true
    });

    wx.login({
      success: (res)=> {
        var authCode = res.code;
        this.getSign(authCode)
      },
      fail: ()=> {
        wx.hideLoading();
        this.setData({
          payLoading: false
        });
        wx.showToast({
          title: '获取用户授权失败',
          icon: 'none'
        });
      }
    })
  },
  getSign(authCode){
    const _self = this;
    const parkInfo = this.data.park;
    parkInfo.code = authCode;
    console.log(parkInfo,'===-------')
    let requestData = {
      amount:parkInfo.amount,
      channel:parkInfo.channel,
      description:parkInfo.description,
      park_id:parkInfo.park_id,
      pay_type:parkInfo.pay_type,
      request_source:parkInfo.request_source,
      title:parkInfo.title,
      trade_no:parkInfo.trade_no,
      wx_app_id:"wx0870f79235e513f7",
      extra:{
        code:authCode,
        car_number: parkInfo.plate_number
      },
      union_id:parkInfo.union_id
    }
    console.log(requestData,'requestData')
    wx.request({
      url: `${app.cloudUrl}/capp/api/user/bolinkUnifiedPay`,
      method: 'POST',
      // dataType: 'text',
      data: requestData,
      header: {
        'content-type': 'application/json' // 默认值
      },
      success(res) {
        let data = res.data;
        console.log(data,'云平台返回数据--------------------')
        if(data.status == 200){
          wx.hideLoading();
          console.log(data.data,'data---')
          
          // let dataInfo = JSON.parse(data.data.data)
          // console.log(dataInfo,'dataInfo')
          _self.pay(authCode,data.data)
        }else{
          wx.hideLoading();
          wx.showToast({
            title: data.errmsg,
            icon: 'none'
          });
        }
        _self.setData({
          payLoading: false
        });
      }
    })
  },
  pay(authCode,parkInfo){
    const _self = this;
    parkInfo.code = authCode;
    console.log(parkInfo,'getparkInfo')
    console.log(app.baseUrl,'app.baseUrl')
    wx.request({
      url: `${app.baseUrl}/unionapi/bolinkunified`,
      method: 'POST',
      data: parkInfo,
      header: {
        'content-type': 'application/json' // 默认值
      },
      success(res) {
        let data = res.data;
        if(data.state === 1){
          wx.hideLoading();
          _self.goPay(data.data)
        }else{
          wx.hideLoading();
          wx.showToast({
            title: data.errmsg,
            icon: 'none'
          });
        }
        _self.setData({
          payLoading: false
        });
      }
    })
  },
  goPay(data){
    wx.requestPayment({
      timeStamp: data.timeStamp,
      nonceStr: data.nonceStr,
      package: data.package,
      signType: data.signType,
      paySign: data.paySign,
      success(res) { 
        if (res.errMsg == "requestPayment:ok"){
          if(app.otherMini){// 支付成功后，如果是小程序跳转过来的直接返回用户小程序
            wx.navigateBackMiniProgram({
              extraData: {
                "source":"s.bolink.club",
                "flag":app.globalData.flag,
                "errMsg":"requestPayment:ok"
              },
              success: (res) => {
                console.log(JSON.stringify(res));
              },
              fail: (res) => {
                console.log(JSON.stringify(res));
              }
            });
          }else {
            wx.redirectTo({
              url: '/pages/result/result?msg=success'
            });
          }
        }else{
          wx.redirectTo({
            url: '/pages/result/result?msg=fail'
          });
        }
      },
      fail(res) {
        console.log('err res--->', res)
        if (res.errMsg =="requestPayment:fail cancel"){
          wx.showToast({
            title: '用户取消支付',
            icon: 'none'
          });
          wx.navigateBackMiniProgram({
            extraData: {},
            success: (res) => {
              console.log(JSON.stringify(res));
            },
            fail: (res) => {
              console.log(JSON.stringify(res));
            }
          });
        }
       }
    })

    
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})