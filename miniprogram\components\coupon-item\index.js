// components/coupon-item/index.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    item: {
      type: Object,
      observer: function(val){
        console.log('val: ', val)
        this._initData(val)
      }
    }
    
  },

  /**
   * 组件的初始数据
   */
  data: {
    class_name:''
  },

  /**
   * 组件的方法列表
   */
  methods: {
    _initData(item) {
      let class_name = '';
      // 时长券
      if (item.state === 1 && item.parking_type === 1) {
        class_name = 'coupon-item__box coupon-item_color_1'
      }
      // 金额券
      else if (item.state === 1 && item.parking_type === 2) {
        class_name = 'coupon-item__box coupon-item_color_2'
      }
      else if (item.state === 2) {
        class_name = 'coupon-item__box coupon-item_color_3'
      }
      else if (item.state === 3) {
        class_name = 'coupon-item__box coupon-item_color_4'
      }
      else if (item.state === 4) {
        class_name = 'coupon-item__box coupon-item_color_5'
      }
      else {
        class_name = 'coupon-item__box'
      }
      this.setData({
        class_name: class_name
      })
    }
  }
})
