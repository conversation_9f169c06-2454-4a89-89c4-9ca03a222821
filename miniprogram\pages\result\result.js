var app = getApp();
Page({
  data: {
    authCode: '',
    chargeRecordId: '',
    app: null,
    msg: 'success',
    parkData: null
  },
  onLoad(query) {
    this.setData({
      chargeRecordId: query.chargeRecordId,
      msg: query.msg,
    })
  },
  // TODO 返回时候的操作
  reBack(event) {
    this.back(this.data.msg);
  },

  back(msg) {
    if (!app.otherMini) {
      if (app.type == 'pay') {
        console.log('直接支付无法返回')
      } else if (app.type == 'order') {
        wx.reLaunch({
          url: '../index/index?union_id=' + app.globalData.union_id + '&park_id=' + app.globalData.park_id
        });
      } else {

      }
      
    } else {
      let message = msg === "success"?"requestPayment:ok":"requestPayment:fail";
      console.log('支付结束后返回小程序requestPayment：', message)
      wx.navigateBackMiniProgram({
        extraData: {
          "source":"s.bolink.club",
          "flag":app.globalData.flag,
          "errMsg":message
        },
        success: (res) => {
          console.log(JSON.stringify(res));
        },
        fail: (res) => {
          console.log(JSON.stringify(res));
        }
      });
    }
  },
  reBackPark() {
    if(app.type == 'pay'){
      wx.reLaunch({
        url: '/pages/park/park?plate_number='+ app.globalData.plate_number 
          + '&union_id=' + app.globalData.union_id
          + '&park_id=' + app.globalData.park_id
          + '&park_name=' + app.globalData.park_name
          + '&money=' + app.globalData.money
          + '&order_id' + app.globalData.order_id
      })
    }else if(app.type == 'order'){
      wx.reLaunch({
        url: '/pages/order/order?plate_number=' + app.globalData.plate_number + '&union_id=' + app.globalData.union_id
          + '&park_id=' + app.globalData.park_id
      })
    }
  },

});