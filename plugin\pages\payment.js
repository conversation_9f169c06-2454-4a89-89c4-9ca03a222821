// plugin/pages/hello-page.js
const { setPaymentResult } = require('../index')
Page({
  data: {
    perOrderInfo: {},
    payStatus: 'waiting'
  },
  onLoad(evt) {
    const self = this;
    console.log('evt: ', evt)
    // 初始化支付结果
    this.setData({
      payStatus: 'waiting'
    })
    setPaymentResult({})
    let paramError = true;
    if (evt && evt.pre_order_info) {
      if (typeof evt.pre_order_info === 'string') {
        console.log('payment stringify: ', evt.pre_order_info)
        try {
          this.setData({
            perOrderInfo: JSON.parse(evt.pre_order_info)
          })
          paramError = false
        } catch (error) {
          
        }
      }
    }
    // 参数异常 - 两秒后返回
    if (paramError) {
      wx.showToast({
        title: '参数异常',
        icon: 'error',
        duration: 2000,
        success() {
          self.paymentResultCakkback()
        }
      })
    } else {
      // 参数正确，尝试直接发起支付
      this.handlePlaceOrder()
    }
  },
  /**
   * 支付结果回调
   * @param { String } type 
   */
  paymentResultCakkback(type, evt) {
    if (type === 'success') {
      setPaymentResult({
        errMsg: evt ? evt.errMsg : 'requestPluginPayment:ok'
      })
      this.setData({
        payStatus: 'success'
      })
    } else {
      this.setData({
        payStatus: 'warn'
      })
      setPaymentResult({
        errMsg: evt ? evt.errMsg : 'requestPluginPayment:fail requestPayment:fail cancel'
      })
    }
    setTimeout(()=> {
      wx.navigateBack({
        delta: 1,
      })
    }, 2000)
  },
  async handlePlaceOrder()  {
    const self = this;
    const { perOrderInfo } = this.data;
    wx.requestPluginPayment({
      version: 'release',
      fee: perOrderInfo.data.amount * 100,
      currencyType: 'CNY',
      paymentArgs: perOrderInfo,
      success(res) { 
        console.log('res success->', res)
        if (res.errMsg == "requestPluginPayment:ok"){
          self.paymentResultCakkback('success')
        }else{
          self.paymentResultCakkback('fail')
        }
      },
      fail(res) {
        console.log('err res--->', res)
        wx.showToast({
          title: '用户取消支付',
          icon: 'none',
          duration: 2000,
          success() {
            self.paymentResultCakkback('fail', res)
          }
        });
       }
    })
  }
})
