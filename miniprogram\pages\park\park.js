// pages/park/park.js
var app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    park:{
      park_name:'',
      union_id:'',
      park_id:'',
      order_id:'',
      plate_number:'',
      money:'0.00',
      
    },
    canRepay: '',
    payLoading:false,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    app.type ='pay';
    if (app.otherMini){
      this.setData({
        'park': app.globalData
      })
    }else{
      app.globalData = options;
      this.setData({
        'park':app.globalData
      })
    }

  },
  //支付下单
  naviteToResult(event) {
    let _self = this;
    if(_self.data.payLoading){
      return false;
    }
    this.setData({
      payLoading: true
    });

    wx.login({
      success: (res)=> {
        var authCode = res.code;
        this.pay(authCode)
      },
      fail: ()=> {
        this.setData({
          payLoading: false
        });
        wx.showToast({
          title: '获取用户授权失败',
          icon: 'none'
        });
      }
    })
  },
  pay(authCode){
    const _self = this;
    const parkInfo = this.data.park;
    parkInfo.code = authCode;
    wx.request({
      url: `${app.baseUrl}/unionapi/miniprogram/pay`,
      method: 'POST',
      data: parkInfo,
      header: {
        'content-type': 'application/json' // 默认值
      },
      success(res) {
        let data = res.data;
        if(data.state === 1){
          _self.goPay(data.data)
        }else{
          wx.showToast({
            title: data.errmsg,
            icon: 'none'
          });
        }
        _self.setData({
          payLoading: false
        });
      }
    })

    // this.goPay()
  },
  goPay(data){
    wx.requestPayment({
      timeStamp: data.timeStamp,
      nonceStr: data.nonceStr,
      package: data.package,
      signType: data.signType,
      paySign: data.paySign,
      success(res) { 
        if (res.errMsg == "requestPayment:ok"){
          if(app.otherMini){// 支付成功后，如果是小程序跳转过来的直接返回用户小程序
            wx.navigateBackMiniProgram({
              extraData: {
                "source":"s.bolink.club",
                "flag":app.globalData.flag,
                "errMsg":"requestPayment:ok"
              },
              success: (res) => {
                console.log(JSON.stringify(res));
              },
              fail: (res) => {
                console.log(JSON.stringify(res));
              }
            });
          }else {
            wx.redirectTo({
              url: '/pages/result/result?msg=success'
            });
          }
          // wx.redirectTo({
          //   url: '/pages/result/result?msg=success'
          // });
        }else{
          wx.redirectTo({
            url: '/pages/result/result?msg=fail'
          });
        }
      },
      fail(res) {
        console.log('err res--->', res)
        if (res.errMsg =="requestPayment:fail cancel"){
          wx.showToast({
            title: '用户取消支付',
            icon: 'none'
          });
        }
       }
    })

    
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})