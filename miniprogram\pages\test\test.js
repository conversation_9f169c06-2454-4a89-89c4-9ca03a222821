// pages/test/test.js
const plugin = requirePlugin('pay-plugin')
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    value: '',
    platformType: '',
    platformText: '',
    extraData: '',
    placeholder: '{"union_id":"***","park_id":"***","pay_url":"***"}',
    payInfo: {},
  },
  getPayInfoFn() {
    const self = this;
    this.getPayInfo({
      pay_platform: 'beta',
      pay_url: ''
    }).then((res) => {
      self.setData({
        payInfo: JSON.stringify(res)
      })
      wx.showToast({
        title: '获取成功',
        icon: 'none',
        duration: 2000
      })
      console.log('payInfo--->', self.data.payInfo)
    })
  },
  radioChange(evt) {
    const { value } = evt.detail
    this.setData({
      value: value
    })
  },
  platformChange(evt) {
    const { value } = evt.detail
    this.setData({
      platformType: value
    })
  },
  handleChange(evt) {
    const { value } = evt.detail
    this.setData({
      extraData: value
    })
  },
  platformValue(evt) {
    const { value } = evt.detail
    this.setData({
      platformText: value
    })
  },
  handleReset() {
    this.setData({
      value: '',
      platformText: '',
      platformType: '',
      extraData: ''
    })
  },
  async handleClick() {
    const { value, platformText, platformType, extraData } = this.data
    app.otherMini = true
    if (value && value === 'pay') {
      this.getPayInfo({
        pay_platform: platformType,
        pay_url: platformText
      }).then(res => {
        wx.navigateTo({
          url: '/pages/pay/pay'
        })
      }).catch(err => {
        wx.showToast({
          title: '资源获取失败',
          icon: 'none',
          duration: 2000
        })
      })
      return false
    }
    if (value && extraData) {
      if (value === 'order') {
        app.globalData = JSON.parse(extraData)
        app.toggleRequestAddress(app)
        wx.navigateTo({
          url: '/pages/order/order'
        })
      } else if (value === 'park') {
        app.globalData = JSON.parse(extraData)
        app.toggleRequestAddress(app)
        wx.navigateTo({
          url: '/pages/park/park'
        })
      } else {}
    } else {
      wx.showToast({
        title: '请输入',
        icon: 'none',
        duration: 2000
      })
      
    }
  },
  /**
   * 获取支付信息
   * @param {*} evt 
   */
  getPayInfo(evt){
    const self = this;
    wx.showLoading({
      title: '加载中',
    })
    return new Promise((resolve, reject) => {
      wx.request({
        url: 'http://***********:8080/test_bolink/callparkapi/appletsExtraData',
        method: 'POST',
        data: evt,
        header: {
          'content-type': 'application/json' // 默认值
        },
        success(res) {
          console.log('res: ', res.data)
          app.globalData = res.data
          app.toggleRequestAddress(app)
          wx.hideLoading({
            success: (res) => {},
          })
          resolve(res.data)
        },
        fail() {
          wx.hideLoading({
            success: (res) => {},
          })
          reject()
        }
      })
    })
    
    
    
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    console.log("plugin methods:", plugin.getPaymentResult())
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})