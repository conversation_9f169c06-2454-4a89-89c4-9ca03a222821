var app = getApp();
Page({
  data: {
    inputDisabled:true,
    clearCarNumber: false,
    maskState: false,
    inputValue: '',
    union_id:'',
    park_id:'',
    type:'order',
    loading:false,
  },
  onLoad(query) {
    if (app.otherMini) {
      this.setData({
        union_id: app.globalData.union_id,
        park_id: app.globalData.park_id,
        inputValue: ''
      })
    } else {
      this.setData({
        union_id:query.union_id,
        park_id:query.park_id,
        inputValue:''
      })
    }
  },
  onShow(){
    if(app.globalData.union_id == undefined || app.globalData.union_id == ''){
      wx.showModal({
        title: '提示',
        content: '请在车场小程序重新发起支付',
        showCancel:false,
        success (res) {
          wx.navigateBackMiniProgram({
            extraData: {},
            success: (res) => {
              console.log(JSON.stringify(res));
            },
            fail: (res) => {
              console.log(JSON.stringify(res));
            }
          });
        }
      })
    }
    this.setData({
      inputValue:''
    })
  },
  bindKeyInput(e) {
    let val = e.detail.value
    this.setData({
      inputValue: val.toUpperCase()
    });
  },

  naviteToPark(event) {
    let _self = this;
    if (this.data.inputValue === '') {
      wx.showToast({
        title: '请输入缴费车牌号',
        icon:'none'
      });
      return;
    }
    if(app.type == 'pay'){
      wx.setStorageSync('plate_number', this.data.inputValue)
      wx.navigateTo({
        url: '/pages/park/park?plate_number=' + this.data.inputValue + '&union_id=' + this.data.union_id
          + '&park_id=' + this.data.park_id
      });
    }else if(app.type == 'order'){
      if(_self.data.loading){
        return false;
      }
      _self.setData({
        loading:true
      })
      wx.request({
        url: app.baseUrl + '/unionapi/miniprogram/getOrderInfo',
        method: 'POST',
        data: {
          plate_number: this.data.inputValue,
          union_id: this.data.union_id,
          park_id: this.data.park_id
        },
        header: {
          'content-type': 'application/json' // 默认值
        },
        success(res) {
          let data = res.data;
          if (data.state === 1) {
            wx.setStorageSync('plate_number', _self.data.inputValue)
            app.globalData.plate_number = _self.data.inputValue;
            wx.navigateTo({
              url: '/pages/order/order?plate_number=' + _self.data.inputValue 
                + '&union_id=' + _self.data.union_id
                + '&park_id=' + _self.data.park_id
            });
          } else if (data.state === 2) {
            wx.setStorageSync('plate_number', _self.data.inputValue);
            app.globalData.plate_number = _self.data.inputValue;
            wx.navigateTo({
              url: '/pages/order/order?plate_number=' + _self.data.inputValue 
                + '&union_id=' + _self.data.union_id
                + '&park_id=' + _self.data.park_id
            });
          }
          else {
            wx.showToast({
              title: data.errmsg,
              icon: 'none',
              duration: 3000
            });
          }
          _self.setData({
            loading: false
          })
        },
        fail(err) {
          console.log('err--->', err)
          _self.setData({
            loading: false
          })
        }
      })
    }  
  },
  currentValFn(val) {
    this.setData({
      inputValue: val.detail
    })
  },
  inputTapFn() {

    this.setData({
      maskState: true
    })
  },
  closeFn(e) {
    this.setData({
      maskState: e.detail
    })
  },
  offKeyboard: function (e) {
    this.setData({
      inputFalse: false
    })
  },
  inputfunc: function (e) {
    let that = this
    let carnumber = (e.detail.value).toUpperCase()
    if (carnumber.length > 8) {
      wx.showToast({
        title: '车牌号长度超过限制！',
        icon: 'none'
      });
      carnumber = that.data.inputValue;
    }
    that.setData({
      inputValue: carnumber
    })
  },
  /**
 * 
 *  当点击车牌用券输入框时
 *  判断是否打开系统键盘
 * 
 * */
  carNumberTap: function (e) {
    if (this.data.inputDisabled) {
      this.setData({
        maskState: true
      })
    }
  },
  /**
 * 键盘切换事件   自定义车牌键盘和系统键盘 
 * input 禁不禁止输入  inputDisabled
 */
  toggleKeyboard() {
    let inputDisabled = this.data.inputDisabled;
    this.setData({
      inputDisabled: !inputDisabled,
      maskState: !inputDisabled,
      inputFalse: !inputDisabled
    })
  },
});
