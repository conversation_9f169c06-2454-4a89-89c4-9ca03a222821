<!--components/keyboard/index.wxml-->
<view class='keyboard_mask' wx:if='{{showMask}}'>
  <view class='top-tap-no' catchtap='close'></view>
  <view class='keyboard_wrapper'>
    <view class='close_keyboard'>
      <image class='closeimg' src='../../../assets/images/arrow_down.png' bindtap='close'></image>
    </view>
    <view class='keyboard-layout-all' wx:if="{{hideKeyboard}}">
      <view class='keyboard-layout'>
        <button class='key-btn van-hairline--top'
         wx:for="{{keyboard.provinceItemFirst}}" 
         wx:for-item='item'
         wx:key="{{item}}" 
         bindtap='clickkey' data-object-text='{{item}}'>{{item}}</button>
      </view>
      <view class='keyboard-layout'>
        <button class='key-btn van-hairline--top'
         wx:for="{{keyboard.provinceItemSecond}}" 
         wx:for-item='item'
         wx:key="{{item}}" 
         bindtap='clickkey' data-object-text='{{item}}'>{{item}}</button>
      </view>
      <view class='keyboard-layout'>
        <button class='key-btn van-hairline--top'
         wx:for="{{keyboard.provinceItemThird}}" 
         wx:for-item='item'
         wx:key="{{item}}" 
         bindtap='clickkey' data-object-text='{{item}}'>{{item}}</button>
      </view>
      <view class='keyboard-layout'>
        <button class='key-btn van-hairline--top'
         wx:for="{{keyboard.provinceItemFourth}}" 
         wx:for-item='item'
         wx:key="{{item}}" 
         bindtap='clickkey' data-object-text='{{item}}'>{{item}}</button>
      </view>
    </view>
    <view class='keyboard-layout-all' wx:else>
      <view class='keyboard-layout'>
        <button class='key-btn van-hairline--top'
         wx:for="{{keyboard.numericKey}}" 
         wx:for-item='item'
         wx:key="{{item}}"
         disabled='{{!(valueLen > 1)}}' 
         bindtap='clickkey' data-object-text='{{item}}'>{{item}}</button>
      </view>
      <view class='keyboard-layout'>
        <button class='key-btn van-hairline--top'
         wx:for="{{keyboard.letterKeyFirst}}" 
         wx:for-item='item'
         wx:key="{{item}}" 
         bindtap='clickkey' data-object-text='{{item}}'>{{item}}</button>
      </view>
      <view class='keyboard-layout'>
        <button class='key-btn van-hairline--top'
         wx:for="{{keyboard.letterKeySecond}}" 
         wx:for-item='item'
         wx:key="{{item}}" 
         bindtap='clickkey' data-object-text='{{item}}'>{{item}}</button>
      </view>
      <view class='keyboard-layout'>
        <button class='key-btn van-hairline--top'
         wx:for="{{keyboard.letterKeyThird}}" 
         wx:for-item='item'
         wx:key="{{item}}" 
         bindtap='clickkey' data-object-text='{{item}}'>{{item}}</button>
         <button class='key-btn van-hairline--top' bindtap='clickkey' data-object-text='del'>
          <image class='del-icon' src='./assets/images/del-icon.png'></image>
         </button>
      </view>
    </view>
  </view>
</view>
