<view class="park">
  <view class="park-card">
    <view class="park-name">{{park.park_name}}</view>
    <view class="park-line mt-12"></view>
    <view class="park-info mt-12" bindtap="editCarNumber">
      <text class="park-info-label mr-10" >车牌号码：</text>
      <text class="park-info-text">{{park.plate_number}}</text>
      <text class="update-carnumber">修改车牌号</text>
    </view>
     <view class="park-info mt-12">
      <text class="park-info-label mr-10">入场时间：</text>
      <text class="park-info-text">{{park.start_time}}</text>
    </view>
     <view class="park-info mt-12">
      <text class="park-info-label mr-10">已停时长：</text>
      <text class="park-info-text">{{park.park_time}}</text>
    </view>
    <view class="park-info mt-12">
      <text class="park-info-label mr-10">停车位置：</text>
      <text class="park-info-text">{{park.stop_position}}</text>
    </view>
    <view class="park-line mt-12"></view>
    <view class="park-info mt-12">
      <text class="park-info-label">减免时长：</text>
      <view class="park-info-fee">
        <text class="park-info-text">{{park.derate_duration?park.derate_duration:0}}</text>
      </view>
    </view>
    <!-- <view class="park-info mt-12"  wx:if="{{park.show_coupon === 1}}">
      <text class="park-info-label">可用优惠：</text>
      <view class="park-info-fee">
        <text class="update-carnumber" bindtap="handleCouponClick">点击领取优惠券</text>
      </view>
    </view> -->
    <view class="park-info mt-12">
      <text class="park-info-label">已付金额：</text>
      <view class="park-info-fee">
        <text class="park-info-text text-right">-￥<text>{{park.prepay?park.prepay:'0.00'}}</text></text>
      </view>
    </view>
    <view class="park-line mt-12"></view>
    <view class="park-pay mt-12">
      <text class="park-pay-label">待支付</text>
      <text class="park-pay-money">￥<text>{{park.money}}</text></text>
    </view>
  </view>
  <view class="park-btn-group">
    <view class="perpay-title" wx:if="{{perpayState}}">您已预付过，不能再次预支付</view>
    <button disabled="{{canRepay}}" loading="{{payLoading}}" type="primary" size="default" class="park-btn" bindtap="naviteToResult" wx:else>立即缴费</button>
  </view>
</view>