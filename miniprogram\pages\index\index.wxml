<view class="page">
  <view class="section">
    <view class="carno-label">缴费车牌号</view>
    <!-- <input class="carno" bindinput="bindKeyInput" value="{{inputValue}}" placeholder="请输入车牌号"/> -->
    <input 
    class="carno"
     bindinput="bindKeyInput"
     bindtap='inputTapFn'
     value="{{inputValue}}"
     maxlength="8"
     disabled='{{true}}'
      placeholder="请输入车牌号"/>
    <button type="primary" bindtap="naviteToPark" :loading="{{loading}}">查 询</button>
  </view>
</view>
<keyboard bind:currentVal="currentValFn"
bind:close="closeFn"
maskState="{{maskState}}"
defaultVal="{{inputValue}}"
clearCarNumber="{{clearCarNumber}}" 
maxLen="{{8}}"
count="{{count}}"
></keyboard>
